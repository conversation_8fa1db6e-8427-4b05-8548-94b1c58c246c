import { Project } from '../types';

export class ExportService {
  // 导出为TXT格式
  static exportToTxt(project: Project): void {
    const content = this.generateTxtContent(project);
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    this.downloadFile(blob, `${project.title}.txt`);
  }

  // 导出为Markdown格式
  static exportToMarkdown(project: Project): void {
    const content = this.generateMarkdownContent(project);
    const blob = new Blob([content], { type: 'text/markdown;charset=utf-8' });
    this.downloadFile(blob, `${project.title}.md`);
  }

  // 导出为JSON格式（项目数据）
  static exportToJson(project: Project): void {
    const content = JSON.stringify(project, null, 2);
    const blob = new Blob([content], { type: 'application/json;charset=utf-8' });
    this.downloadFile(blob, `${project.title}_data.json`);
  }

  // 生成TXT内容
  private static generateTxtContent(project: Project): string {
    let content = '';
    
    // 标题和基本信息
    content += `${project.title}\n`;
    content += `${'='.repeat(project.title.length)}\n\n`;
    
    if (project.genre) {
      content += `类型：${project.genre}\n`;
    }
    
    if (project.theme) {
      content += `主题：${project.theme}\n`;
    }
    
    content += `创建时间：${project.createdAt.toLocaleDateString()}\n`;
    content += `最后更新：${project.updatedAt.toLocaleDateString()}\n\n`;
    
    // 大纲
    if (project.outline) {
      content += `故事大纲\n`;
      content += `${'='.repeat(8)}\n\n`;
      content += `${project.outline}\n\n`;
    }
    
    // 章节内容
    if (project.chapters.length > 0) {
      content += `正文\n`;
      content += `${'='.repeat(4)}\n\n`;
      
      project.chapters.forEach((chapter, index) => {
        content += `第${index + 1}章 ${chapter.title}\n`;
        content += `${'-'.repeat(20)}\n\n`;
        content += `${chapter.content}\n\n\n`;
      });
    }
    
    // 统计信息
    const totalWords = project.chapters.reduce((total, chapter) => total + chapter.wordCount, 0);
    content += `统计信息\n`;
    content += `${'='.repeat(8)}\n\n`;
    content += `总字数：${totalWords}\n`;
    content += `章节数：${project.chapters.length}\n`;
    content += `导出时间：${new Date().toLocaleString()}\n`;
    
    return content;
  }

  // 生成Markdown内容
  private static generateMarkdownContent(project: Project): string {
    let content = '';
    
    // 标题和基本信息
    content += `# ${project.title}\n\n`;
    
    if (project.genre || project.theme) {
      content += `## 基本信息\n\n`;
      if (project.genre) {
        content += `- **类型**：${project.genre}\n`;
      }
      if (project.theme) {
        content += `- **主题**：${project.theme}\n`;
      }
      content += `- **创建时间**：${project.createdAt.toLocaleDateString()}\n`;
      content += `- **最后更新**：${project.updatedAt.toLocaleDateString()}\n\n`;
    }
    
    // 大纲
    if (project.outline) {
      content += `## 故事大纲\n\n`;
      content += `${project.outline}\n\n`;
    }
    
    // 章节内容
    if (project.chapters.length > 0) {
      content += `## 正文\n\n`;
      
      project.chapters.forEach((chapter, index) => {
        content += `### 第${index + 1}章 ${chapter.title}\n\n`;
        content += `${chapter.content}\n\n`;
      });
    }
    
    // 统计信息
    const totalWords = project.chapters.reduce((total, chapter) => total + chapter.wordCount, 0);
    content += `## 统计信息\n\n`;
    content += `- **总字数**：${totalWords}\n`;
    content += `- **章节数**：${project.chapters.length}\n`;
    content += `- **导出时间**：${new Date().toLocaleString()}\n`;
    
    return content;
  }

  // 下载文件
  private static downloadFile(blob: Blob, filename: string): void {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  // 获取导出预览
  static getExportPreview(project: Project, format: 'txt' | 'markdown'): string {
    switch (format) {
      case 'txt':
        return this.generateTxtContent(project);
      case 'markdown':
        return this.generateMarkdownContent(project);
      default:
        return '';
    }
  }

  // 获取项目统计信息
  static getProjectStats(project: Project) {
    const totalWords = project.chapters.reduce((total, chapter) => total + chapter.wordCount, 0);
    const averageWordsPerChapter = project.chapters.length > 0 ? Math.round(totalWords / project.chapters.length) : 0;
    
    return {
      totalWords,
      chapterCount: project.chapters.length,
      averageWordsPerChapter,
      createdAt: project.createdAt,
      updatedAt: project.updatedAt,
      hasOutline: !!project.outline,
      outlineLength: project.outline ? project.outline.length : 0,
    };
  }

  // 验证项目是否可以导出
  static canExport(project: Project): { canExport: boolean; reason?: string } {
    if (!project.title) {
      return { canExport: false, reason: '项目缺少标题' };
    }
    
    if (project.chapters.length === 0) {
      return { canExport: false, reason: '项目没有章节内容' };
    }
    
    const hasContent = project.chapters.some(chapter => chapter.content.trim().length > 0);
    if (!hasContent) {
      return { canExport: false, reason: '所有章节都没有内容' };
    }
    
    return { canExport: true };
  }

  // 批量导出多个项目
  static exportMultipleProjects(projects: Project[], format: 'txt' | 'markdown' | 'json'): void {
    if (projects.length === 0) return;
    
    if (projects.length === 1) {
      // 单个项目直接导出
      switch (format) {
        case 'txt':
          this.exportToTxt(projects[0]);
          break;
        case 'markdown':
          this.exportToMarkdown(projects[0]);
          break;
        case 'json':
          this.exportToJson(projects[0]);
          break;
      }
      return;
    }
    
    // 多个项目打包导出
    const timestamp = new Date().toISOString().slice(0, 10);
    let combinedContent = '';
    
    if (format === 'json') {
      // JSON格式：导出项目数组
      const content = JSON.stringify(projects, null, 2);
      const blob = new Blob([content], { type: 'application/json;charset=utf-8' });
      this.downloadFile(blob, `novels_export_${timestamp}.json`);
      return;
    }
    
    // TXT或Markdown格式：合并所有项目
    projects.forEach((project, index) => {
      if (index > 0) {
        combinedContent += '\n\n' + '='.repeat(50) + '\n\n';
      }
      
      if (format === 'txt') {
        combinedContent += this.generateTxtContent(project);
      } else {
        combinedContent += this.generateMarkdownContent(project);
      }
    });
    
    const mimeType = format === 'txt' ? 'text/plain' : 'text/markdown';
    const extension = format === 'txt' ? 'txt' : 'md';
    const blob = new Blob([combinedContent], { type: `${mimeType};charset=utf-8` });
    this.downloadFile(blob, `novels_export_${timestamp}.${extension}`);
  }
}
