import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Loader2, Save, RefreshCw, X } from 'lucide-react';
import { useProjectStore } from '../stores/useProjectStore';
import { useAIStore } from '../stores/useAIStore';
import { AIService } from '../utils/aiService';

interface ChapterGeneratorProps {
  projectId: string;
  chapterNumber?: number;
  onClose: () => void;
}

const ChapterGenerator: React.FC<ChapterGeneratorProps> = ({ 
  projectId, 
  chapterNumber, 
  onClose 
}) => {
  const { projects, addChapter } = useProjectStore();
  const { settings, isGenerating, setGenerating } = useAIStore();
  const [chapterTitle, setChapterTitle] = useState('');
  const [generatedContent, setGeneratedContent] = useState('');
  const [targetLength, setTargetLength] = useState(2000);
  const [error, setError] = useState('');

  const project = projects.find(p => p.id === projectId);

  if (!project) {
    return null;
  }

  const nextChapterNumber = chapterNumber || project.chapters.length + 1;

  const handleGenerate = async () => {
    if (!settings) {
      setError('请先在设置中配置AI参数');
      return;
    }

    if (!chapterTitle.trim()) {
      setError('请输入章节标题');
      return;
    }

    if (!project.outline) {
      setError('请先生成项目大纲');
      return;
    }

    try {
      setError('');
      setGenerating(true);
      
      const aiService = new AIService(settings);
      
      // 获取前面章节的概要
      const previousChapters = project.chapters
        .slice(0, nextChapterNumber - 1)
        .map((chapter, index) => 
          `第${index + 1}章 ${chapter.title}：${chapter.content.substring(0, 200)}...`
        );

      const content = await aiService.generateChapter({
        title: project.title,
        outline: project.outline,
        chapterTitle,
        chapterNumber: nextChapterNumber,
        previousChapters,
        style: project.settings.style,
        targetLength,
      });

      setGeneratedContent(content);
    } catch (err) {
      setError(err instanceof Error ? err.message : '生成章节失败');
    } finally {
      setGenerating(false);
    }
  };

  const handleSave = () => {
    if (generatedContent && chapterTitle) {
      addChapter(projectId, {
        title: chapterTitle,
        content: generatedContent,
      });
      onClose();
    }
  };

  const handleRegenerate = () => {
    setGeneratedContent('');
    handleGenerate();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <BookOpen className="h-6 w-6 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-900">AI章节生成</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6">
          {/* 项目信息 */}
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <h3 className="font-medium text-gray-900 mb-2">
              {project.title} - 第{nextChapterNumber}章
            </h3>
            <p className="text-sm text-gray-600">
              已有章节：{project.chapters.length}章
            </p>
          </div>

          {/* 章节设置 */}
          {!generatedContent && (
            <div className="space-y-4 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  章节标题 *
                </label>
                <input
                  type="text"
                  value={chapterTitle}
                  onChange={(e) => setChapterTitle(e.target.value)}
                  placeholder="输入章节标题"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  目标字数
                </label>
                <select
                  value={targetLength}
                  onChange={(e) => setTargetLength(parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value={1000}>约1000字</option>
                  <option value={2000}>约2000字</option>
                  <option value={3000}>约3000字</option>
                  <option value={4000}>约4000字</option>
                </select>
              </div>
            </div>
          )}

          {/* 错误提示 */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
              <p className="text-red-800">{error}</p>
            </div>
          )}

          {/* 生成按钮 */}
          {!generatedContent && (
            <div className="text-center py-8">
              <BookOpen className="h-16 w-16 text-blue-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                准备生成章节内容
              </h3>
              <p className="text-gray-600 mb-6">
                AI将根据大纲和前面章节的内容生成新章节
              </p>
              <button
                onClick={handleGenerate}
                disabled={isGenerating || !chapterTitle.trim()}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center space-x-2 mx-auto"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="h-5 w-5 animate-spin" />
                    <span>生成中...</span>
                  </>
                ) : (
                  <>
                    <BookOpen className="h-5 w-5" />
                    <span>生成章节</span>
                  </>
                )}
              </button>
            </div>
          )}

          {/* 生成结果 */}
          {generatedContent && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">
                  第{nextChapterNumber}章 {chapterTitle}
                </h3>
                <div className="flex space-x-2">
                  <button
                    onClick={handleRegenerate}
                    disabled={isGenerating}
                    className="px-4 py-2 text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors disabled:opacity-50 flex items-center space-x-2"
                  >
                    {isGenerating ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <RefreshCw className="h-4 w-4" />
                    )}
                    <span>重新生成</span>
                  </button>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                <div className="text-sm text-gray-800 whitespace-pre-wrap">
                  {generatedContent}
                </div>
              </div>

              <div className="flex items-center justify-between text-sm text-gray-600">
                <span>字数：{generatedContent.length}</span>
                <span>目标：{targetLength}字</span>
              </div>

              <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                <button
                  onClick={onClose}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={handleSave}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
                >
                  <Save className="h-4 w-4" />
                  <span>保存章节</span>
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChapterGenerator;
