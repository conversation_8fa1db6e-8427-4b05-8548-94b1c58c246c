import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import ChapterEditor from '../components/ChapterEditor';

const ChapterEdit: React.FC = () => {
  const { projectId, chapterId } = useParams<{ projectId: string; chapterId?: string }>();
  const navigate = useNavigate();

  if (!projectId) {
    navigate('/');
    return null;
  }

  const handleClose = () => {
    navigate(`/project/${projectId}`);
  };

  return (
    <ChapterEditor
      projectId={projectId}
      chapterId={chapterId}
      onClose={handleClose}
    />
  );
};

export default ChapterEdit;
