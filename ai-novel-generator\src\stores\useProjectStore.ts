import { create } from 'zustand';
import { Project, Chapter } from '../types';
import { StorageService } from '../utils/storage';

interface ProjectState {
  projects: Project[];
  currentProject: Project | null;
  loading: boolean;
  error: string | null;
}

interface ProjectActions {
  // 项目操作
  loadProjects: () => void;
  createProject: (project: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateProject: (projectId: string, updates: Partial<Project>) => void;
  deleteProject: (projectId: string) => void;
  setCurrentProject: (projectId: string) => void;
  
  // 章节操作
  addChapter: (projectId: string, chapter: Omit<Chapter, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateChapter: (projectId: string, chapterId: string, updates: Partial<Chapter>) => void;
  deleteChapter: (projectId: string, chapterId: string) => void;
  
  // 工具方法
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

type ProjectStore = ProjectState & ProjectActions;

// 生成唯一ID
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

export const useProjectStore = create<ProjectStore>((set, get) => ({
  // 初始状态
  projects: [],
  currentProject: null,
  loading: false,
  error: null,

  // 加载项目列表
  loadProjects: () => {
    try {
      set({ loading: true, error: null });
      const projects = StorageService.getProjects();
      set({ projects, loading: false });
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : '加载项目失败',
        loading: false 
      });
    }
  },

  // 创建新项目
  createProject: (projectData) => {
    try {
      set({ loading: true, error: null });
      
      const newProject: Project = {
        ...projectData,
        id: generateId(),
        createdAt: new Date(),
        updatedAt: new Date(),
        chapters: [],
      };

      StorageService.saveProject(newProject);
      
      const { projects } = get();
      set({ 
        projects: [...projects, newProject],
        currentProject: newProject,
        loading: false 
      });
      
      StorageService.setCurrentProjectId(newProject.id);
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : '创建项目失败',
        loading: false 
      });
    }
  },

  // 更新项目
  updateProject: (projectId, updates) => {
    try {
      const { projects, currentProject } = get();
      const projectIndex = projects.findIndex(p => p.id === projectId);
      
      if (projectIndex === -1) {
        throw new Error('项目不存在');
      }

      const updatedProject = {
        ...projects[projectIndex],
        ...updates,
        updatedAt: new Date(),
      };

      StorageService.saveProject(updatedProject);
      
      const newProjects = [...projects];
      newProjects[projectIndex] = updatedProject;
      
      set({
        projects: newProjects,
        currentProject: currentProject?.id === projectId ? updatedProject : currentProject,
      });
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : '更新项目失败'
      });
    }
  },

  // 删除项目
  deleteProject: (projectId) => {
    try {
      StorageService.deleteProject(projectId);
      
      const { projects, currentProject } = get();
      const newProjects = projects.filter(p => p.id !== projectId);
      
      set({
        projects: newProjects,
        currentProject: currentProject?.id === projectId ? null : currentProject,
      });
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : '删除项目失败'
      });
    }
  },

  // 设置当前项目
  setCurrentProject: (projectId) => {
    const { projects } = get();
    const project = projects.find(p => p.id === projectId);
    
    if (project) {
      set({ currentProject: project });
      StorageService.setCurrentProjectId(projectId);
    }
  },

  // 添加章节
  addChapter: (projectId, chapterData) => {
    try {
      const newChapter: Chapter = {
        ...chapterData,
        id: generateId(),
        createdAt: new Date(),
        updatedAt: new Date(),
        wordCount: chapterData.content.length,
      };

      const { projects } = get();
      const projectIndex = projects.findIndex(p => p.id === projectId);
      
      if (projectIndex === -1) {
        throw new Error('项目不存在');
      }

      const updatedProject = {
        ...projects[projectIndex],
        chapters: [...projects[projectIndex].chapters, newChapter],
        updatedAt: new Date(),
      };

      get().updateProject(projectId, { chapters: updatedProject.chapters });
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : '添加章节失败'
      });
    }
  },

  // 更新章节
  updateChapter: (projectId, chapterId, updates) => {
    try {
      const { projects } = get();
      const project = projects.find(p => p.id === projectId);
      
      if (!project) {
        throw new Error('项目不存在');
      }

      const chapterIndex = project.chapters.findIndex(c => c.id === chapterId);
      if (chapterIndex === -1) {
        throw new Error('章节不存在');
      }

      const updatedChapter = {
        ...project.chapters[chapterIndex],
        ...updates,
        updatedAt: new Date(),
        wordCount: updates.content ? updates.content.length : project.chapters[chapterIndex].wordCount,
      };

      const newChapters = [...project.chapters];
      newChapters[chapterIndex] = updatedChapter;

      get().updateProject(projectId, { chapters: newChapters });
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : '更新章节失败'
      });
    }
  },

  // 删除章节
  deleteChapter: (projectId, chapterId) => {
    try {
      const { projects } = get();
      const project = projects.find(p => p.id === projectId);
      
      if (!project) {
        throw new Error('项目不存在');
      }

      const newChapters = project.chapters.filter(c => c.id !== chapterId);
      get().updateProject(projectId, { chapters: newChapters });
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : '删除章节失败'
      });
    }
  },

  // 清除错误
  clearError: () => set({ error: null }),

  // 设置加载状态
  setLoading: (loading) => set({ loading }),
}));
