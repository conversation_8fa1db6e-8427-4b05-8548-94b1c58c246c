import React, { useState, useEffect } from 'react';
import { Save, ArrowLeft, FileText, Clock, Wand2 } from 'lucide-react';
import { useProjectStore } from '../stores/useProjectStore';
import { Chapter } from '../types';

interface ChapterEditorProps {
  projectId: string;
  chapterId?: string;
  onClose: () => void;
}

const ChapterEditor: React.FC<ChapterEditorProps> = ({ projectId, chapterId, onClose }) => {
  const { projects, addChapter, updateChapter } = useProjectStore();
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  const project = projects.find(p => p.id === projectId);
  const chapter = chapterId ? project?.chapters.find(c => c.id === chapterId) : null;
  const isEditing = !!chapterId;

  useEffect(() => {
    if (chapter) {
      setTitle(chapter.title);
      setContent(chapter.content);
    }
  }, [chapter]);

  // 自动保存功能
  useEffect(() => {
    if (!isEditing || !title || !content) return;

    const autoSaveTimer = setTimeout(() => {
      handleSave(false);
    }, 5000); // 5秒后自动保存

    return () => clearTimeout(autoSaveTimer);
  }, [title, content, isEditing]);

  const handleSave = async (showFeedback = true) => {
    if (!title.trim() || !content.trim()) {
      if (showFeedback) {
        alert('请输入标题和内容');
      }
      return;
    }

    try {
      setIsSaving(true);
      
      if (isEditing && chapterId) {
        updateChapter(projectId, chapterId, {
          title: title.trim(),
          content: content.trim(),
        });
      } else {
        addChapter(projectId, {
          title: title.trim(),
          content: content.trim(),
        });
      }
      
      setLastSaved(new Date());
      
      if (showFeedback) {
        alert('保存成功！');
      }
    } catch (error) {
      if (showFeedback) {
        alert('保存失败，请重试');
      }
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveAndClose = async () => {
    await handleSave();
    onClose();
  };

  const getWordCount = () => {
    return content.length;
  };

  const getChapterNumber = () => {
    if (!project) return 1;
    if (isEditing && chapter) {
      return project.chapters.findIndex(c => c.id === chapterId) + 1;
    }
    return project.chapters.length + 1;
  };

  if (!project) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-white z-50 flex flex-col">
      {/* 头部工具栏 */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={onClose}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
            <div>
              <h1 className="text-xl font-semibold text-gray-900">
                {isEditing ? '编辑章节' : '新建章节'}
              </h1>
              <p className="text-sm text-gray-600">
                {project.title} - 第{getChapterNumber()}章
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* 状态信息 */}
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <div className="flex items-center space-x-1">
                <FileText className="h-4 w-4" />
                <span>{getWordCount()} 字</span>
              </div>
              {lastSaved && (
                <div className="flex items-center space-x-1">
                  <Clock className="h-4 w-4" />
                  <span>已保存 {lastSaved.toLocaleTimeString()}</span>
                </div>
              )}
            </div>
            
            {/* 操作按钮 */}
            <div className="flex space-x-2">
              <button
                onClick={() => handleSave()}
                disabled={isSaving}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
              >
                <Save className="h-4 w-4" />
                <span>{isSaving ? '保存中...' : '保存'}</span>
              </button>
              
              <button
                onClick={handleSaveAndClose}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
              >
                <span>保存并关闭</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 编辑区域 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 主编辑器 */}
        <div className="flex-1 flex flex-col">
          {/* 标题输入 */}
          <div className="px-6 py-4 border-b border-gray-200">
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="输入章节标题..."
              className="w-full text-2xl font-bold text-gray-900 placeholder-gray-400 border-none outline-none bg-transparent"
            />
          </div>
          
          {/* 内容编辑器 */}
          <div className="flex-1 px-6 py-4">
            <textarea
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder="开始写作..."
              className="w-full h-full text-gray-800 placeholder-gray-400 border-none outline-none resize-none bg-transparent leading-relaxed"
              style={{ fontSize: '16px', lineHeight: '1.8' }}
            />
          </div>
        </div>
        
        {/* 侧边栏工具 */}
        <div className="w-80 bg-gray-50 border-l border-gray-200 p-4 overflow-y-auto">
          <div className="space-y-6">
            {/* 章节信息 */}
            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-3">章节信息</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">字数：</span>
                  <span className="font-medium">{getWordCount()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">章节：</span>
                  <span className="font-medium">第{getChapterNumber()}章</span>
                </div>
                {chapter && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">创建时间：</span>
                    <span className="font-medium">{chapter.createdAt.toLocaleDateString()}</span>
                  </div>
                )}
              </div>
            </div>
            
            {/* AI辅助工具 */}
            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-3">AI辅助</h3>
              <div className="space-y-2">
                <button className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors flex items-center space-x-2">
                  <Wand2 className="h-4 w-4" />
                  <span>续写内容</span>
                </button>
                <button className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors flex items-center space-x-2">
                  <Wand2 className="h-4 w-4" />
                  <span>润色文本</span>
                </button>
                <button className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors flex items-center space-x-2">
                  <Wand2 className="h-4 w-4" />
                  <span>生成对话</span>
                </button>
              </div>
            </div>
            
            {/* 写作提示 */}
            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-3">写作提示</h3>
              <div className="text-xs text-gray-600 space-y-2">
                <p>• 每5秒自动保存</p>
                <p>• 使用Ctrl+S手动保存</p>
                <p>• 建议每章2000-4000字</p>
                <p>• 注意段落分明，对话清晰</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChapterEditor;
