// 项目数据结构
export interface Project {
  id: string;
  title: string;
  genre: string; // 类型
  theme: string; // 主题
  createdAt: Date;
  updatedAt: Date;
  outline: string; // 大纲
  chapters: Chapter[];
  settings: ProjectSettings;
}

// 章节数据结构
export interface Chapter {
  id: string;
  title: string;
  content: string;
  wordCount: number;
  createdAt: Date;
  updatedAt: Date;
}

// 项目设置
export interface ProjectSettings {
  style: string; // 写作风格
  length: string; // 目标长度
  complexity: string; // 复杂度
}

// AI设置
export interface AISettings {
  apiKey: string;
  model: string;
  temperature: number;
  maxTokens: number;
}

// 生成参数
export interface GenerationParams {
  type: 'outline' | 'chapter';
  prompt: string;
  temperature?: number;
  maxTokens?: number;
}
