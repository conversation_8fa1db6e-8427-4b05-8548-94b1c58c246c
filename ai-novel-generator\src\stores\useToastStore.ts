import { create } from 'zustand';
import { ToastProps, ToastType } from '../components/Toast';

interface ToastState {
  toasts: ToastProps[];
}

interface ToastActions {
  addToast: (toast: Omit<ToastProps, 'id' | 'onClose'>) => void;
  removeToast: (id: string) => void;
  clearToasts: () => void;
  showSuccess: (title: string, message?: string) => void;
  showError: (title: string, message?: string) => void;
  showWarning: (title: string, message?: string) => void;
  showInfo: (title: string, message?: string) => void;
}

type ToastStore = ToastState & ToastActions;

// 生成唯一ID
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

export const useToastStore = create<ToastStore>((set, get) => ({
  toasts: [],

  addToast: (toastData) => {
    const id = generateId();
    const toast: ToastProps = {
      ...toastData,
      id,
      onClose: get().removeToast,
    };

    set((state) => ({
      toasts: [...state.toasts, toast],
    }));
  },

  removeToast: (id) => {
    set((state) => ({
      toasts: state.toasts.filter((toast) => toast.id !== id),
    }));
  },

  clearToasts: () => {
    set({ toasts: [] });
  },

  showSuccess: (title, message) => {
    get().addToast({
      type: 'success',
      title,
      message,
      duration: 4000,
    });
  },

  showError: (title, message) => {
    get().addToast({
      type: 'error',
      title,
      message,
      duration: 6000,
    });
  },

  showWarning: (title, message) => {
    get().addToast({
      type: 'warning',
      title,
      message,
      duration: 5000,
    });
  },

  showInfo: (title, message) => {
    get().addToast({
      type: 'info',
      title,
      message,
      duration: 4000,
    });
  },
}));
