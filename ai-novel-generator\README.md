# AI创梦笔 - AI小说生成器

一个基于React的纯前端AI小说生成器，让AI助力您的创作之旅。

## ✨ 功能特性

### 📝 项目管理
- 创建和管理多个小说项目
- 设置项目基本信息（标题、类型、主题、风格等）
- 项目列表展示和快速访问
- 项目删除和编辑功能

### 🤖 AI内容生成
- **大纲生成**：根据项目信息自动生成详细的故事大纲
- **章节生成**：基于大纲和前文生成新章节内容
- **内容续写**：在编辑器中续写现有章节
- **文本润色**：优化和改进已有文本
- **角色生成**：创建生动的角色描述

### ✍️ 编辑器功能
- 全屏编辑模式，专注写作体验
- 实时字数统计
- 自动保存功能（每5秒）
- 章节管理和组织
- AI辅助工具侧边栏

### 📤 导出功能
- **TXT格式**：纯文本格式，兼容性最好
- **Markdown格式**：支持格式化的文本
- **JSON格式**：完整项目数据，可重新导入
- 导出预览功能
- 批量导出支持

### ⚙️ 设置和配置
- OpenAI API密钥配置
- 模型选择（GPT-3.5、GPT-4等）
- 生成参数调节（创意度、最大字数）
- 本地数据存储，隐私安全

## 🚀 快速开始

### 环境要求
- Node.js 16+
- 现代浏览器（Chrome、Firefox、Safari、Edge）

### 安装和运行

1. 安装依赖
```bash
npm install
```

2. 启动开发服务器
```bash
npm run dev
```

3. 打开浏览器访问 `http://localhost:5173`

### 配置AI服务

1. 访问 [OpenAI API Keys](https://platform.openai.com/api-keys) 获取API密钥
2. 在应用的设置页面配置您的API密钥
3. 选择合适的模型和参数
4. 开始使用AI生成功能

## 🛠️ 技术栈

- **前端框架**：React 18 + TypeScript
- **构建工具**：Vite
- **样式框架**：Tailwind CSS
- **状态管理**：Zustand
- **路由**：React Router
- **图标**：Lucide React
- **AI服务**：OpenAI API

## 🔒 隐私和安全

- 所有项目数据仅存储在您的浏览器本地
- API密钥安全存储在localStorage中
- 不会上传任何内容到我们的服务器
- 支持数据导出和备份

## 📝 使用说明

1. **创建项目**：点击"新建项目"填写基本信息
2. **生成大纲**：使用AI根据项目信息生成故事大纲
3. **创作章节**：手动编写或使用AI生成章节内容
4. **编辑润色**：在编辑器中完善和优化内容
5. **导出作品**：选择合适的格式导出您的小说

---

**AI创梦笔** - 让AI助力您的创作之旅 ✨
