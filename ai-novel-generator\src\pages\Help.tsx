import React from 'react';
import { BookOpen, Wand2, Download, Settings, PenTool, HelpCircle } from 'lucide-react';

const Help: React.FC = () => {
  const sections = [
    {
      id: 'getting-started',
      title: '快速开始',
      icon: BookOpen,
      content: [
        '1. 首先在设置页面配置您的OpenAI API密钥',
        '2. 点击"新建项目"创建您的第一个小说项目',
        '3. 填写项目基本信息：标题、类型、主题等',
        '4. 使用AI生成大纲或手动编写',
        '5. 开始创作章节内容',
      ],
    },
    {
      id: 'ai-features',
      title: 'AI功能',
      icon: Wand2,
      content: [
        '大纲生成：根据项目信息自动生成详细的故事大纲',
        '章节生成：基于大纲和前文生成新章节内容',
        '内容续写：在编辑器中续写现有章节',
        '文本润色：优化和改进已有文本',
        '角色生成：创建生动的角色描述',
      ],
    },
    {
      id: 'editor',
      title: '编辑器使用',
      icon: PenTool,
      content: [
        '自动保存：编辑器每5秒自动保存内容',
        '字数统计：实时显示章节和总字数',
        '快捷键：Ctrl+S 手动保存',
        '侧边栏：提供AI辅助工具和写作提示',
        '全屏编辑：专注的写作环境',
      ],
    },
    {
      id: 'export',
      title: '导出功能',
      icon: Download,
      content: [
        'TXT格式：纯文本格式，兼容性最好',
        'Markdown格式：支持格式化的文本',
        'JSON格式：完整项目数据，可重新导入',
        '批量导出：支持导出多个项目',
        '预览功能：导出前可预览内容',
      ],
    },
    {
      id: 'settings',
      title: '设置说明',
      icon: Settings,
      content: [
        'API密钥：您的OpenAI API密钥，安全存储在本地',
        '模型选择：支持GPT-3.5、GPT-4等模型',
        '创意度：控制AI生成内容的创意程度',
        '最大字数：单次生成的最大字数限制',
        '数据安全：所有数据仅存储在您的浏览器中',
      ],
    },
  ];

  const faqs = [
    {
      question: '如何获取OpenAI API密钥？',
      answer: '访问 https://platform.openai.com/api-keys 注册账户并创建API密钥。请妥善保管您的密钥。',
    },
    {
      question: '数据会被上传到服务器吗？',
      answer: '不会。所有项目数据都存储在您的浏览器本地，我们不会收集或上传任何内容。',
    },
    {
      question: 'AI生成的内容质量如何？',
      answer: 'AI生成质量取决于您提供的信息详细程度和选择的模型。建议提供详细的项目信息以获得更好的结果。',
    },
    {
      question: '可以离线使用吗？',
      answer: '编辑功能可以离线使用，但AI生成功能需要网络连接来调用OpenAI API。',
    },
    {
      question: '如何备份我的项目？',
      answer: '可以使用导出功能将项目导出为JSON格式进行备份，也可以导出为TXT或Markdown格式保存。',
    },
  ];

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      <div className="text-center">
        <HelpCircle className="h-16 w-16 text-blue-600 mx-auto mb-4" />
        <h1 className="text-3xl font-bold text-gray-900 mb-2">使用帮助</h1>
        <p className="text-gray-600">了解如何使用AI创梦笔创作您的小说</p>
      </div>

      {/* 功能介绍 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {sections.map((section) => (
          <div key={section.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <section.icon className="h-6 w-6 text-blue-600" />
              <h2 className="text-lg font-semibold text-gray-900">{section.title}</h2>
            </div>
            <ul className="space-y-2">
              {section.content.map((item, index) => (
                <li key={index} className="text-sm text-gray-700 flex items-start">
                  <span className="text-blue-600 mr-2">•</span>
                  <span>{item}</span>
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>

      {/* 常见问题 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">常见问题</h2>
        <div className="space-y-6">
          {faqs.map((faq, index) => (
            <div key={index}>
              <h3 className="text-lg font-medium text-gray-900 mb-2">{faq.question}</h3>
              <p className="text-gray-700">{faq.answer}</p>
            </div>
          ))}
        </div>
      </div>

      {/* 技术支持 */}
      <div className="bg-blue-50 rounded-lg border border-blue-200 p-6">
        <h2 className="text-lg font-semibold text-blue-900 mb-3">需要帮助？</h2>
        <p className="text-blue-800 mb-4">
          如果您在使用过程中遇到问题，可以：
        </p>
        <ul className="space-y-2 text-blue-800">
          <li className="flex items-start">
            <span className="text-blue-600 mr-2">•</span>
            <span>检查浏览器控制台是否有错误信息</span>
          </li>
          <li className="flex items-start">
            <span className="text-blue-600 mr-2">•</span>
            <span>确认API密钥配置正确</span>
          </li>
          <li className="flex items-start">
            <span className="text-blue-600 mr-2">•</span>
            <span>尝试刷新页面或清除浏览器缓存</span>
          </li>
        </ul>
      </div>

      {/* 版本信息 */}
      <div className="text-center text-sm text-gray-500">
        <p>AI创梦笔 v1.0.0 - 让AI助力您的创作之旅</p>
      </div>
    </div>
  );
};

export default Help;
