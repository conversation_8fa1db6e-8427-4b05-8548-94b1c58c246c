import { Project, AISettings } from '../types/index.ts';

// 本地存储键名
const STORAGE_KEYS = {
  PROJECTS: 'ai-novel-projects',
  AI_SETTINGS: 'ai-novel-settings',
  CURRENT_PROJECT: 'ai-novel-current-project',
} as const;

// 项目存储服务
export class StorageService {
  // 获取所有项目
  static getProjects(): Project[] {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.PROJECTS);
      if (!data) return [];
      
      const projects = JSON.parse(data);
      // 转换日期字符串为Date对象
      return projects.map((project: any) => ({
        ...project,
        createdAt: new Date(project.createdAt),
        updatedAt: new Date(project.updatedAt),
        chapters: project.chapters.map((chapter: any) => ({
          ...chapter,
          createdAt: new Date(chapter.createdAt),
          updatedAt: new Date(chapter.updatedAt),
        })),
      }));
    } catch (error) {
      console.error('获取项目数据失败:', error);
      return [];
    }
  }

  // 保存项目
  static saveProject(project: Project): void {
    try {
      const projects = this.getProjects();
      const existingIndex = projects.findIndex(p => p.id === project.id);
      
      if (existingIndex >= 0) {
        projects[existingIndex] = project;
      } else {
        projects.push(project);
      }
      
      localStorage.setItem(STORAGE_KEYS.PROJECTS, JSON.stringify(projects));
    } catch (error) {
      console.error('保存项目失败:', error);
      throw new Error('保存项目失败');
    }
  }

  // 删除项目
  static deleteProject(projectId: string): void {
    try {
      const projects = this.getProjects();
      const filteredProjects = projects.filter(p => p.id !== projectId);
      localStorage.setItem(STORAGE_KEYS.PROJECTS, JSON.stringify(filteredProjects));
    } catch (error) {
      console.error('删除项目失败:', error);
      throw new Error('删除项目失败');
    }
  }

  // 获取单个项目
  static getProject(projectId: string): Project | null {
    const projects = this.getProjects();
    return projects.find(p => p.id === projectId) || null;
  }

  // 获取AI设置
  static getAISettings(): AISettings | null {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.AI_SETTINGS);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('获取AI设置失败:', error);
      return null;
    }
  }

  // 保存AI设置
  static saveAISettings(settings: AISettings): void {
    try {
      localStorage.setItem(STORAGE_KEYS.AI_SETTINGS, JSON.stringify(settings));
    } catch (error) {
      console.error('保存AI设置失败:', error);
      throw new Error('保存AI设置失败');
    }
  }

  // 获取当前项目ID
  static getCurrentProjectId(): string | null {
    return localStorage.getItem(STORAGE_KEYS.CURRENT_PROJECT);
  }

  // 设置当前项目ID
  static setCurrentProjectId(projectId: string): void {
    localStorage.setItem(STORAGE_KEYS.CURRENT_PROJECT, projectId);
  }

  // 清除所有数据
  static clearAll(): void {
    Object.values(STORAGE_KEYS).forEach(key => {
      localStorage.removeItem(key);
    });
  }

  // 导出数据
  static exportData(): string {
    const data = {
      projects: this.getProjects(),
      aiSettings: this.getAISettings(),
      exportDate: new Date().toISOString(),
    };
    return JSON.stringify(data, null, 2);
  }

  // 导入数据
  static importData(jsonData: string): void {
    try {
      const data = JSON.parse(jsonData);
      
      if (data.projects) {
        localStorage.setItem(STORAGE_KEYS.PROJECTS, JSON.stringify(data.projects));
      }
      
      if (data.aiSettings) {
        localStorage.setItem(STORAGE_KEYS.AI_SETTINGS, JSON.stringify(data.aiSettings));
      }
    } catch (error) {
      console.error('导入数据失败:', error);
      throw new Error('导入数据失败：数据格式不正确');
    }
  }
}
