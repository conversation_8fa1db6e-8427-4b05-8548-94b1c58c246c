import { AISettings, GenerationParams } from '../types';

export class AIService {
  private settings: AISettings;

  constructor(settings: AISettings) {
    this.settings = settings;
  }

  // 验证API密钥是否存在
  private validateApiKey(): void {
    if (!this.settings.apiKey) {
      throw new Error('请先在设置中配置OpenAI API密钥');
    }
  }

  // 调用OpenAI API
  private async callOpenAI(messages: any[], options?: Partial<GenerationParams>): Promise<string> {
    this.validateApiKey();

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.settings.apiKey}`,
      },
      body: JSON.stringify({
        model: this.settings.model,
        messages,
        temperature: options?.temperature || this.settings.temperature,
        max_tokens: options?.maxTokens || this.settings.maxTokens,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'AI生成失败');
    }

    const data = await response.json();
    return data.choices[0]?.message?.content || '';
  }

  // 生成小说大纲
  async generateOutline(params: {
    title: string;
    genre: string;
    theme: string;
    style: string;
    length: string;
    complexity: string;
  }): Promise<string> {
    const prompt = this.buildOutlinePrompt(params);
    
    const messages = [
      {
        role: 'system',
        content: '你是一位专业的小说创作助手，擅长构思精彩的故事大纲。请根据用户提供的信息，创作一个详细、有趣且逻辑清晰的小说大纲。'
      },
      {
        role: 'user',
        content: prompt
      }
    ];

    return await this.callOpenAI(messages);
  }

  // 生成章节内容
  async generateChapter(params: {
    title: string;
    outline: string;
    chapterTitle: string;
    chapterNumber: number;
    previousChapters?: string[];
    style: string;
    targetLength?: number;
  }): Promise<string> {
    const prompt = this.buildChapterPrompt(params);
    
    const messages = [
      {
        role: 'system',
        content: '你是一位专业的小说作家，擅长创作引人入胜的章节内容。请根据大纲和要求，创作出生动、有趣且符合整体故事发展的章节内容。'
      },
      {
        role: 'user',
        content: prompt
      }
    ];

    const options = {
      maxTokens: params.targetLength ? Math.min(params.targetLength * 2, 4000) : this.settings.maxTokens
    };

    return await this.callOpenAI(messages, options);
  }

  // 续写章节内容
  async continueChapter(params: {
    existingContent: string;
    outline: string;
    chapterTitle: string;
    style: string;
    targetLength?: number;
  }): Promise<string> {
    const prompt = `
请继续以下章节的内容：

章节标题：${params.chapterTitle}
已有内容：
${params.existingContent}

故事大纲：
${params.outline}

写作风格：${params.style}
${params.targetLength ? `目标长度：约${params.targetLength}字` : ''}

请自然地续写这个章节，保持风格一致，推进故事情节。
`;

    const messages = [
      {
        role: 'system',
        content: '你是一位专业的小说作家，擅长续写章节内容。请保持与已有内容的风格一致，自然地推进故事发展。'
      },
      {
        role: 'user',
        content: prompt
      }
    ];

    const options = {
      maxTokens: params.targetLength ? Math.min(params.targetLength * 2, 4000) : this.settings.maxTokens
    };

    return await this.callOpenAI(messages, options);
  }

  // 构建大纲生成提示词
  private buildOutlinePrompt(params: {
    title: string;
    genre: string;
    theme: string;
    style: string;
    length: string;
    complexity: string;
  }): string {
    return `
请为以下小说创作一个详细的大纲：

小说标题：${params.title}
类型：${params.genre}
主题：${params.theme}
写作风格：${params.style}
目标长度：${params.length}
复杂度：${params.complexity}

请包含以下内容：
1. 故事背景和设定
2. 主要角色介绍（包括主角、配角、反派等）
3. 主要情节线索
4. 章节大纲（建议10-20章）
5. 故事的高潮和结局

请确保大纲逻辑清晰、情节引人入胜，符合${params.genre}类型的特点。
`;
  }

  // 构建章节生成提示词
  private buildChapterPrompt(params: {
    title: string;
    outline: string;
    chapterTitle: string;
    chapterNumber: number;
    previousChapters?: string[];
    style: string;
    targetLength?: number;
  }): string {
    let prompt = `
请根据以下信息创作小说章节：

小说标题：${params.title}
章节标题：第${params.chapterNumber}章 ${params.chapterTitle}
写作风格：${params.style}
${params.targetLength ? `目标长度：约${params.targetLength}字` : ''}

故事大纲：
${params.outline}
`;

    if (params.previousChapters && params.previousChapters.length > 0) {
      prompt += `

前面章节概要：
${params.previousChapters.join('\n\n')}
`;
    }

    prompt += `

请创作这一章的内容，要求：
1. 符合整体故事大纲
2. 与前面章节保持连贯性
3. 情节生动有趣，人物形象鲜明
4. 语言流畅，符合${params.style}风格
5. 适当的对话和描写
`;

    return prompt;
  }

  // 优化和润色文本
  async polishText(text: string, style: string): Promise<string> {
    const messages = [
      {
        role: 'system',
        content: `你是一位专业的文字编辑，擅长润色和优化小说文本。请保持原文的意思和情节，但改善语言表达、修正语法错误、增强可读性。`
      },
      {
        role: 'user',
        content: `请润色以下文本，使其更符合${style}风格：\n\n${text}`
      }
    ];

    return await this.callOpenAI(messages);
  }

  // 生成角色描述
  async generateCharacter(params: {
    name: string;
    role: string;
    genre: string;
    background?: string;
  }): Promise<string> {
    const prompt = `
请为${params.genre}小说创作一个角色：

角色姓名：${params.name}
角色定位：${params.role}
${params.background ? `背景设定：${params.background}` : ''}

请包含：
1. 外貌特征
2. 性格特点
3. 背景故事
4. 能力特长
5. 在故事中的作用

请确保角色形象鲜明、有特色，符合${params.genre}类型的设定。
`;

    const messages = [
      {
        role: 'system',
        content: '你是一位专业的角色设计师，擅长创作生动有趣的小说角色。'
      },
      {
        role: 'user',
        content: prompt
      }
    ];

    return await this.callOpenAI(messages);
  }
}
