import { create } from 'zustand';
import { AISettings } from '../types';
import { StorageService } from '../utils/storage';

interface AIState {
  settings: AISettings | null;
  isGenerating: boolean;
  error: string | null;
}

interface AIActions {
  loadSettings: () => void;
  saveSettings: (settings: AISettings) => void;
  setGenerating: (generating: boolean) => void;
  clearError: () => void;
}

type AIStore = AIState & AIActions;

// 默认AI设置
const defaultSettings: AISettings = {
  apiKey: '',
  model: 'gpt-3.5-turbo',
  temperature: 0.7,
  maxTokens: 2000,
};

export const useAIStore = create<AIStore>((set, get) => ({
  // 初始状态
  settings: null,
  isGenerating: false,
  error: null,

  // 加载设置
  loadSettings: () => {
    try {
      const settings = StorageService.getAISettings();
      set({ settings: settings || defaultSettings });
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : '加载AI设置失败',
        settings: defaultSettings 
      });
    }
  },

  // 保存设置
  saveSettings: (settings) => {
    try {
      StorageService.saveAISettings(settings);
      set({ settings, error: null });
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : '保存AI设置失败'
      });
    }
  },

  // 设置生成状态
  setGenerating: (generating) => set({ isGenerating: generating }),

  // 清除错误
  clearError: () => set({ error: null }),
}));
