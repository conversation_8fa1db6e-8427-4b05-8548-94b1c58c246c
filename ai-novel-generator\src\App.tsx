import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Layout from './components/Layout';
import Home from './pages/Home';
import Settings from './pages/Settings';
import ProjectDetail from './pages/ProjectDetail';
import ChapterEdit from './pages/ChapterEdit';
import Help from './pages/Help';
import ToastContainer from './components/ToastContainer';
import { useToastStore } from './stores/useToastStore';

function App() {
  const { toasts, removeToast } = useToastStore();

  return (
    <Router>
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route index element={<Home />} />
          <Route path="project/:id" element={<ProjectDetail />} />
          <Route path="settings" element={<Settings />} />
          <Route path="help" element={<Help />} />
        </Route>
        <Route path="project/:projectId/chapter/new" element={<ChapterEdit />} />
        <Route path="project/:projectId/chapter/:chapterId" element={<ChapterEdit />} />
      </Routes>

      <ToastContainer toasts={toasts} onClose={removeToast} />
    </Router>
  );
}

export default App;
