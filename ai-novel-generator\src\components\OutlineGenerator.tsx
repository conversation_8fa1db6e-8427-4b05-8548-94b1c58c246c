import React, { useState } from 'react';
import { Wand2, Loader2, Save, RefreshCw } from 'lucide-react';
import { useProjectStore } from '../stores/useProjectStore';
import { useAIStore } from '../stores/useAIStore';
import { AIService } from '../utils/aiService';

interface OutlineGeneratorProps {
  projectId: string;
  onClose: () => void;
}

const OutlineGenerator: React.FC<OutlineGeneratorProps> = ({ projectId, onClose }) => {
  const { projects, updateProject } = useProjectStore();
  const { settings, isGenerating, setGenerating } = useAIStore();
  const [generatedOutline, setGeneratedOutline] = useState('');
  const [error, setError] = useState('');

  const project = projects.find(p => p.id === projectId);

  if (!project) {
    return null;
  }

  const handleGenerate = async () => {
    if (!settings) {
      setError('请先在设置中配置AI参数');
      return;
    }

    try {
      setError('');
      setGenerating(true);
      
      const aiService = new AIService(settings);
      const outline = await aiService.generateOutline({
        title: project.title,
        genre: project.genre,
        theme: project.theme,
        style: project.settings.style,
        length: project.settings.length,
        complexity: project.settings.complexity,
      });

      setGeneratedOutline(outline);
    } catch (err) {
      setError(err instanceof Error ? err.message : '生成大纲失败');
    } finally {
      setGenerating(false);
    }
  };

  const handleSave = () => {
    if (generatedOutline) {
      updateProject(projectId, { outline: generatedOutline });
      onClose();
    }
  };

  const handleRegenerate = () => {
    setGeneratedOutline('');
    handleGenerate();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <Wand2 className="h-6 w-6 text-purple-600" />
            <h2 className="text-xl font-semibold text-gray-900">AI大纲生成</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ×
          </button>
        </div>

        <div className="p-6">
          {/* 项目信息 */}
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <h3 className="font-medium text-gray-900 mb-2">项目信息</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">标题：</span>
                <span className="font-medium">{project.title}</span>
              </div>
              <div>
                <span className="text-gray-600">类型：</span>
                <span className="font-medium">{project.genre}</span>
              </div>
              <div>
                <span className="text-gray-600">主题：</span>
                <span className="font-medium">{project.theme}</span>
              </div>
              <div>
                <span className="text-gray-600">风格：</span>
                <span className="font-medium">{project.settings.style}</span>
              </div>
            </div>
          </div>

          {/* 错误提示 */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
              <p className="text-red-800">{error}</p>
            </div>
          )}

          {/* 生成按钮 */}
          {!generatedOutline && (
            <div className="text-center py-8">
              <Wand2 className="h-16 w-16 text-purple-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                准备生成AI大纲
              </h3>
              <p className="text-gray-600 mb-6">
                基于您的项目信息，AI将为您生成详细的故事大纲
              </p>
              <button
                onClick={handleGenerate}
                disabled={isGenerating}
                className="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50 flex items-center space-x-2 mx-auto"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="h-5 w-5 animate-spin" />
                    <span>生成中...</span>
                  </>
                ) : (
                  <>
                    <Wand2 className="h-5 w-5" />
                    <span>生成大纲</span>
                  </>
                )}
              </button>
            </div>
          )}

          {/* 生成结果 */}
          {generatedOutline && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">生成的大纲</h3>
                <div className="flex space-x-2">
                  <button
                    onClick={handleRegenerate}
                    disabled={isGenerating}
                    className="px-4 py-2 text-purple-600 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors disabled:opacity-50 flex items-center space-x-2"
                  >
                    {isGenerating ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <RefreshCw className="h-4 w-4" />
                    )}
                    <span>重新生成</span>
                  </button>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono">
                  {generatedOutline}
                </pre>
              </div>

              <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                <button
                  onClick={onClose}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={handleSave}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
                >
                  <Save className="h-4 w-4" />
                  <span>保存大纲</span>
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OutlineGenerator;
