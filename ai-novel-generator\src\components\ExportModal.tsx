import React, { useState } from 'react';
import { Download, FileText, File, Code, X, Eye } from 'lucide-react';
import { Project } from '../types';
import { ExportService } from '../utils/exportService';
import { useToastStore } from '../stores/useToastStore';

interface ExportModalProps {
  project: Project;
  isOpen: boolean;
  onClose: () => void;
}

const ExportModal: React.FC<ExportModalProps> = ({ project, isOpen, onClose }) => {
  const { showSuccess, showError } = useToastStore();
  const [selectedFormat, setSelectedFormat] = useState<'txt' | 'markdown' | 'json'>('txt');
  const [showPreview, setShowPreview] = useState(false);
  const [previewContent, setPreviewContent] = useState('');

  if (!isOpen) return null;

  const stats = ExportService.getProjectStats(project);
  const { canExport, reason } = ExportService.canExport(project);

  const formats = [
    {
      id: 'txt' as const,
      name: 'TXT文本',
      description: '纯文本格式，兼容性最好',
      icon: FileText,
      extension: '.txt',
    },
    {
      id: 'markdown' as const,
      name: 'Markdown',
      description: '支持格式化的文本格式',
      icon: File,
      extension: '.md',
    },
    {
      id: 'json' as const,
      name: 'JSON数据',
      description: '包含完整项目数据，可重新导入',
      icon: Code,
      extension: '.json',
    },
  ];

  const handleExport = () => {
    if (!canExport) {
      showError('无法导出', reason || '项目数据不完整');
      return;
    }

    try {
      switch (selectedFormat) {
        case 'txt':
          ExportService.exportToTxt(project);
          break;
        case 'markdown':
          ExportService.exportToMarkdown(project);
          break;
        case 'json':
          ExportService.exportToJson(project);
          break;
      }
      showSuccess('导出成功', `${project.title} 已成功导出为 ${selectedFormat.toUpperCase()} 格式`);
      onClose();
    } catch (error) {
      showError('导出失败', '请检查浏览器权限并重试');
    }
  };

  const handlePreview = () => {
    if (selectedFormat === 'json') {
      setPreviewContent(JSON.stringify(project, null, 2));
    } else {
      setPreviewContent(ExportService.getExportPreview(project, selectedFormat));
    }
    setShowPreview(true);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <Download className="h-6 w-6 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-900">导出小说</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6">
          {/* 项目信息 */}
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <h3 className="font-medium text-gray-900 mb-2">{project.title}</h3>
            <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
              <div>总字数：{stats.totalWords}</div>
              <div>章节数：{stats.chapterCount}</div>
              <div>平均每章：{stats.averageWordsPerChapter}字</div>
              <div>包含大纲：{stats.hasOutline ? '是' : '否'}</div>
            </div>
          </div>

          {/* 导出格式选择 */}
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">选择导出格式</h3>
            <div className="space-y-3">
              {formats.map((format) => (
                <label
                  key={format.id}
                  className={`flex items-center p-4 border rounded-lg cursor-pointer transition-colors ${
                    selectedFormat === format.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:bg-gray-50'
                  }`}
                >
                  <input
                    type="radio"
                    name="format"
                    value={format.id}
                    checked={selectedFormat === format.id}
                    onChange={(e) => setSelectedFormat(e.target.value as any)}
                    className="sr-only"
                  />
                  <format.icon className="h-6 w-6 text-gray-600 mr-3" />
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-gray-900">{format.name}</span>
                      <span className="text-xs text-gray-500">{format.extension}</span>
                    </div>
                    <p className="text-sm text-gray-600">{format.description}</p>
                  </div>
                  {selectedFormat === format.id && (
                    <div className="w-4 h-4 bg-blue-600 rounded-full flex items-center justify-center">
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    </div>
                  )}
                </label>
              ))}
            </div>
          </div>

          {/* 错误提示 */}
          {!canExport && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <p className="text-red-800">无法导出：{reason}</p>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex justify-between">
            <button
              onClick={handlePreview}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors flex items-center space-x-2"
            >
              <Eye className="h-4 w-4" />
              <span>预览</span>
            </button>
            
            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                取消
              </button>
              <button
                onClick={handleExport}
                disabled={!canExport}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
              >
                <Download className="h-4 w-4" />
                <span>导出</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 预览模态框 */}
      {showPreview && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                导出预览 - {formats.find(f => f.id === selectedFormat)?.name}
              </h3>
              <button
                onClick={() => setShowPreview(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
            
            <div className="p-6">
              <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono">
                  {previewContent}
                </pre>
              </div>
              
              <div className="flex justify-end mt-4">
                <button
                  onClick={() => setShowPreview(false)}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  关闭预览
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ExportModal;
